// Variables pour le menu
$menu-bg: #0B120C;
$menu-fg-secondary: #C8CEC9;
$hamburger-icon-border: rgba(0, 0, 0, 0.1);

.nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100svh;
  pointer-events: none;
  overflow: hidden;
  z-index: 999;
}

.menuBar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  pointer-events: all;
  color: $menu-fg-secondary;
  z-index: 1000;
  box-sizing: border-box;

  // Utiliser le même padding que .container en mobile
  @media (max-width: 768px) {
    padding-left: var(--container-padding);
    padding-right: var(--container-padding);
    padding-top: var(--container-padding);
    padding-bottom: var(--container-padding);
  }
}

.menuLogo {
  width: 2rem;
  height: 2rem;
  
  a {
    display: block;
    width: 100%;
    height: 100%;
  }
  
  svg {
    width: auto;
    height: 100%;
  }
}

.menuToggleBtn {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
}

.menuToggleLabel {
  overflow: hidden;
  
  p {
    position: relative;
    color: #0B120C;
    transform: translateY(0%);
    will-change: transform;
    font-size: 0.95rem;
    font-weight: 500;
    margin: 0;
  }
}

.menuHamburgerIcon {
  position: relative;
  width: 2rem;
  height: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.3rem;
  
  span {
    position: absolute;
    width: 16px;
    height: 1px;
    background-color: #000000;
    transition: all 0.75s cubic-bezier(0.87, 0, 0.13, 1);
    transform-origin: center;
    will-change: transform;
    border-radius: 1px;
    
    &:nth-child(1) {
      transform: translateY(-3px);
    }
    
    &:nth-child(2) {
      transform: translateY(3px);
    }
  }
  
  &.active {
    span {
      background-color: #fff;
      &:nth-child(1) {
        transform: translateY(0) rotate(45deg) scaleX(1.05);
      }
      
      &:nth-child(2) {
        transform: translateY(0) rotate(-45deg) scaleX(1.05);
      }
    }
  }
}

.menuLogoIcon {
  fill: #FF413D;
  transition: fill 0.75s cubic-bezier(0.87, 0, 0.13, 1);

  &.active {
    fill: #fff;
  }
}

.menuFooterLinks {
  display: inline-block;
  width: fit-content;
}

.menuLogoLetters {
  fill: #0B120C;
  transition: fill 0.75s cubic-bezier(0.87, 0, 0.13, 1);

  &.active {
    fill: #fff;
  }
}

.menuOverlay,
.menuOverlayContent {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100svh;
  color: #C8CEC9;
  overflow: hidden;
  z-index: 1;
}

.menuOverlay {
  background-color: $menu-bg;
  clip-path: polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%);
  will-change: clip-path;
}

.menuOverlayContent {
  display: flex;
  transform: translateY(-50%);
  will-change: transform;
  pointer-events: all;
}

.menuMediaWrapper {
  flex: 2;
  opacity: 0;
  will-change: opacity;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 1;
  }
}

.menuContentWrapper {
  flex: 3;
  position: relative;
  display: flex;
}

.menuContentMain {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-sizing: border-box;
}

.menuFooter {
  margin: 0 auto;
}

.menuContentMain,
.menuFooter {
  width: 75%;
  padding: 2rem;
  display: flex;
  align-items: flex-end;
  gap: 2rem;

  // Utiliser le même padding que .container en mobile
  @media (max-width: 768px) {
    padding-left: var(--container-padding);
    padding-right: var(--container-padding);
  }
}

.menuCol {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  
  &:nth-child(1) {
    flex: 3;
  }
  
  &:nth-child(2) {
    flex: 2;
  }
}

.menuLink {
  transition: opacity 0.3s ease;

  a {
    font-size: 3.5rem;
    font-weight: 500;
    line-height: 1.2;
    letter-spacing: -1px;
    text-decoration: none;
    color: #C8CEC9;
    transition: color 0.3s ease;

    &:hover {
      color: #C8CEC9;
    }
  }
}

// Effet de focus : quand on survole le conteneur, réduire l'opacité de tous les liens
.menuCol:hover .menuLink {
  opacity: 0.3;
}

// Quand on survole un lien spécifique, lui redonner l'opacité complète
.menuCol:hover .menuLink:hover {
  opacity: 1;
}

.menuTag {
  a {
    color: $menu-fg-secondary;
    font-size: 1.5rem;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.3s ease;
    
    &:hover {
      color: #C8CEC9;
    }
  }
}

.menuFooter {
  p {
    color: $menu-fg-secondary;
    font-size: 0.95rem;
    font-weight: 500;
    margin: 0;
  }
}

.line {
  position: relative;
  will-change: transform;
}

.container {
  position: relative;
  transform: translateY(0svh);
  will-change: transform;
}

// Media queries
@media (max-width: 1000px) {
  .menuMediaWrapper {
    display: none;
  }
  
  .menuContentMain,
  .menuFooter {
    width: 100%;
  }
  
  .menuContentMain {
    top: 50%;
    flex-direction: column;
    align-items: flex-start;
    gap: 5rem;
  }
  
  .menuLink {
    a {
      font-size: 3rem;
    }
  }
  
  .menuTag {
    a {
      font-size: 1.25rem;
    }
  }
}
