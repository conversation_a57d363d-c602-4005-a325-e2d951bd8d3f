// Variables pour le menu
$menu-bg: #0f0f0f;
$menu-fg-secondary: #5f5f5f;
$hamburger-icon-border: rgba(255, 255, 255, 0.1);

.nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100svh;
  pointer-events: none;
  overflow: hidden;
  z-index: 999;
}

.menuBar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  pointer-events: all;
  color: $menu-fg-secondary;
  z-index: 1000;
}

.menuLogo {
  width: 2rem;
  height: 2rem;
  
  a {
    display: block;
    width: 100%;
    height: 100%;
  }
  
  svg {
    width: auto;
    height: 100%;
  }
}

.menuToggleBtn {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
}

.menuToggleLabel {
  overflow: hidden;
  
  p {
    position: relative;
    transform: translateY(0%);
    will-change: transform;
    font-size: 0.95rem;
    font-weight: 500;
    margin: 0;
  }
}

.menuHamburgerIcon {
  position: relative;
  width: 3rem;
  height: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.3rem;
  border: 1px solid $hamburger-icon-border;
  border-radius: 100%;
  
  span {
    position: absolute;
    width: 15px;
    height: 1.25px;
    background-color: #fff;
    transition: all 0.75s cubic-bezier(0.87, 0, 0.13, 1);
    transform-origin: center;
    will-change: transform;
    
    &:nth-child(1) {
      transform: translateY(-3px);
    }
    
    &:nth-child(2) {
      transform: translateY(3px);
    }
  }
  
  &.active {
    span {
      &:nth-child(1) {
        transform: translateY(0) rotate(45deg) scaleX(1.05);
      }
      
      &:nth-child(2) {
        transform: translateY(0) rotate(-45deg) scaleX(1.05);
      }
    }
  }
}

.menuOverlay,
.menuOverlayContent {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100svh;
  color: #fff;
  overflow: hidden;
  z-index: 1;
}

.menuOverlay {
  background-color: $menu-bg;
  clip-path: polygon(0% 0%, 100% 0%, 100% 0%, 0% 0%);
  will-change: clip-path;
}

.menuOverlayContent {
  display: flex;
  transform: translateY(-50%);
  will-change: transform;
  pointer-events: all;
}

.menuMediaWrapper {
  flex: 2;
  opacity: 0;
  will-change: opacity;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.25;
  }
}

.menuContentWrapper {
  flex: 3;
  position: relative;
  display: flex;
}

.menuContentMain {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.menuFooter {
  margin: 0 auto;
}

.menuContentMain,
.menuFooter {
  width: 75%;
  padding: 2rem;
  display: flex;
  align-items: flex-end;
  gap: 2rem;
}

.menuCol {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  
  &:nth-child(1) {
    flex: 3;
  }
  
  &:nth-child(2) {
    flex: 2;
  }
}

.menuLink {
  a {
    font-size: 3.5rem;
    font-weight: 500;
    line-height: 1.2;
    text-decoration: none;
    color: #fff;
    transition: color 0.3s ease;
    
    &:hover {
      color: #FF413D;
    }
  }
}

.menuTag {
  a {
    color: $menu-fg-secondary;
    font-size: 1.5rem;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.3s ease;
    
    &:hover {
      color: #fff;
    }
  }
}

.menuFooter {
  p {
    color: $menu-fg-secondary;
    font-size: 0.95rem;
    font-weight: 500;
    margin: 0;
  }
}

.line {
  position: relative;
  will-change: transform;
}

.container {
  position: relative;
  transform: translateY(0svh);
  will-change: transform;
}

// Media queries
@media (max-width: 1000px) {
  .menuMediaWrapper {
    display: none;
  }
  
  .menuContentMain,
  .menuFooter {
    width: 100%;
  }
  
  .menuContentMain {
    top: 50%;
    flex-direction: column;
    align-items: flex-start;
    gap: 5rem;
  }
  
  .menuLink {
    a {
      font-size: 3rem;
    }
  }
  
  .menuTag {
    a {
      font-size: 1.25rem;
    }
  }
}
