'use client';
import { useEffect, useRef, useState } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { useTranslation } from '@/hooks/useTranslation';
import gsap from 'gsap';
import styles from './style.module.scss';
import MenuOverlay from './MenuOverlay';

export default function Header({ locale = 'fr' }) {
  const { t } = useTranslation('navigation');
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const menuToggleLabelRef = useRef(null);
  const hamburgerIconRef = useRef(null);
  const [containerRef, setContainerRef] = useState(null);

  useEffect(() => {
    // Charger CustomEase dynamiquement
    if (typeof window !== 'undefined') {
      import('gsap/CustomEase').then((module) => {
        gsap.registerPlugin(module.CustomEase);
        module.CustomEase.create("hop", ".87,0,.13,1");
      });
    }

    // Récupérer la référence du container de page avec un délai
    const getContainerRef = () => {
      if (typeof window !== 'undefined' && window.pageContainerRef) {
        setContainerRef(window.pageContainerRef);
      } else {
        // Réessayer après un court délai
        setTimeout(getContainerRef, 100);
      }
    };

    getContainerRef();
  }, []);

  useEffect(() => {
    if (isMenuOpen) setIsMenuOpen(false);
  }, [pathname]);

  const toggleMenu = () => {
    if (isAnimating) return;
    console.log('Toggling menu:', !isMenuOpen);
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className={styles.nav}>
      <div className={styles.menuBar}>
        <div className={styles.menuLogo}>
          <Link href={`/${locale}`}>
            <motion.svg
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.3 }}
              width="103"
              height="22"
              viewBox="0 0 103 22"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.1876 10.4951L1.56061e-05 21.2403L0 12.5288L10.7126 1.36566e-05L18.4924 0L9.21587 10.462L9.21584 10.462L9.18763 10.495L9.1876 10.4951ZM1.56061e-05 8.85196V1.36566e-05H7.56835L1.56061e-05 8.85196ZM18.4924 21.7958H2.65005L10.5709 12.5288L18.4924 21.7958Z"
                fill="#FF413D"
              />
            </motion.svg>
          </Link>
        </div>
        <div className={styles.menuToggleBtn} onClick={toggleMenu}>
          <div className={styles.menuToggleLabel}>
            <p ref={menuToggleLabelRef}>{t('menu')}</p>
          </div>
          <div
            className={`${styles.menuHamburgerIcon} ${isMenuOpen ? styles.active : ''}`}
            ref={hamburgerIconRef}
          >
            <span></span>
            <span></span>
          </div>
        </div>
      </div>

      <MenuOverlay
        isOpen={isMenuOpen}
        locale={locale}
        isAnimating={isAnimating}
        setIsAnimating={setIsAnimating}
        menuToggleLabelRef={menuToggleLabelRef}
        hamburgerIconRef={hamburgerIconRef}
        containerRef={containerRef}
      />
    </nav>
  );
}
